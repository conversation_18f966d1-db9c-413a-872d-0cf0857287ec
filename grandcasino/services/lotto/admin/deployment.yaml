---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grandcasino-lotto-admin
  namespace: grandcasino
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grandcasino-lotto-admin
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: grandcasino-lotto-admin
    spec:
      containers:
        - name: grandcasino-lotto-admin
          image: codecrafterteam/lotto-admin-v2:v1.0.17
          imagePullPolicy: Always
          env:
            - name: NEXT_PUBLIC_API_ENDPOINT
              value: "https://api-lotto.grandcasino.bet/api/v1"
            - name: NEXT_PUBLIC_APP_NAME
              value: "GrandCasino Lotto Admin"
            - name: NEXT_PUBLIC_COMPANY_ID
              value: "2"
            - name: NEXT_PUBLIC_TINY_MCE_KEY
              value: "0lr2joji8bt4wfhfxnylkgttyer8czodepcmpabf3ffijlh6"
            - name: NEXT_PUBLIC_IMG_LOGO
              value: "/logo/grandcasino.png"
            - name: NEXT_PUBLIC_PUSHER_KEY
              value: "31b28ef020f236daecf4"
            - name: NEXT_PUBLIC_PUSHER_CLUSTER
              value: "ap1"
            - name: NEXT_PUBLIC_STANDALONE
              value: "true"
            - name: NEXT_PUBLIC_CONNECT_WALLET
              value: "false"
          ports:
            - name: http
              containerPort: 3000
