---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grandcasino-lotto-web
  namespace: grandcasino
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grandcasino-lotto-web
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: grandcasino-lotto-web
    spec:
      containers:
        - name: demo-v2-web
          image: codecrafterteam/lotto-web-v2:v1.0.21
          imagePullPolicy: Always
          env:
            - name: NEXT_PUBLIC_NODE_ENV
              value: "production"
            - name: NEXT_PUBLIC_APP_THEME
              value: "lotto88"
            - name: NEXT_PUBLIC_APP_NAME
              value: "Grand Casino"
            - name: NEXT_PUBLIC_COMPANY_ID
              value: "2"
            - name: NEXT_PUBLIC_FAVICON
              value: "/assets/grandcasino/favicon/favicon-32x32.png"
            - name: NEXT_PUBLIC_API_ENDPOINT
              value: "https://api-lotto.grandcasino.bet/api/v1"
            - name: NEXT_PUBLIC_WALLET_ENDPOINT
              value: "https://api.grandcasino.bet/api"
            - name: NEXT_PUBLIC_MAIN_PROVIDER
              value: "game"
            - name: NEXT_PUBLIC_GAME_URL
              value: "https://grandcasino.bet"
            - name: NEXT_PUBLIC_CDN_URL
              value: "https://cdn.tidtech.dev"
            - name: NEXT_PUBLIC_LOGO
              value: "https://cdn.tidtech.dev/lotto/grandcasino/logo.webp"
            - name: NEXT_PUBLIC_BG
              value: "https://cdn.tidtech.dev/lotto/demo/lotto88/bg-main.jpg"
            - name: NEXT_PUBLIC_MAIN_BG
              value: "https://cdn.tidtech.dev/lotto/demo/lotto88/bg-main.jpg"
            - name: NEXT_PUBLIC_MAIN_BG_FOOTER
              value: ""
          ports:
            - name: http
              containerPort: 3000