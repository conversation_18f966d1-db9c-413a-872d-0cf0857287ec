---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grandcasino-api
  namespace: grandcasino
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grandcasino-api
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: grandcasino-api
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: grandcasino-api
          image: codecrafterteam/tidtech-api:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          volumeMounts:
            - name: google-service-account-volume
              mountPath: /secrets/
          envFrom:
            - secretRef:
                name: grandcasino-api-env-sync
          command: ["/bin/sh", "-c", "cp /secrets/google_service_account.json /app/google_service_account.json && ./build/API"]
      volumes:
        - name: google-service-account-volume
          secret:
            secretName: google-service-account-volume
