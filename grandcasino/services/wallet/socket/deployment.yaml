---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grandcasino-socket
  namespace: grandcasino
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grandcasino-socket
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: grandcasino-socket
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: grandcasino-socket
          image: cyberrich/cybergame-socket:dev
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: grandcasino-socket-env
          ports:
            - name: http
              containerPort: 3000