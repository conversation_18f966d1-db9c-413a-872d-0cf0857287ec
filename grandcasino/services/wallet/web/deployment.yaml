---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grandcasino-web
  namespace: grandcasino
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grandcasino-web
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: grandcasino-web
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: grandcasino-web
          image: cyberrich/cybergame-web:v1.2
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: grandcasino-env-sync
