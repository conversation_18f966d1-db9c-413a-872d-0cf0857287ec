---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: grandcasino-admin-env-external
  namespace: grandcasino
spec:
  refreshInterval: 1h  # ดึงข้อมูลใหม่ทุก 1 ชั่วโมง
  secretStoreRef:
    name: aws-secretstore-grandcasino
    kind: SecretStore
  target:
    name: grandcasino-admin-env-sync  # ชื่อ Kubernetes Secret ที่จะถูกสร้าง
    creationPolicy: Owner
  dataFrom:
    - extract:
        key: grandcasino-admin-env