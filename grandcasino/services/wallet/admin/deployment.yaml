---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grandcasino-admin
  namespace: grandcasino
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grandcasino-admin
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: grandcasino-admin
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: grandcasino-admin
          image: codecrafterteam/tidtech-admin:dev-latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: grandcasino-admin-env-sync
