apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-grandcasino
  namespace: grandcasino
spec:
  rules:
    - host: grandcasino.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: grandcasino-web
                port:
                  number: 3000
    - host: www.grandcasino.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: grandcasino-web
                port:
                  number: 3000
    - host: admin.grandcasino.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: grandcasino-admin
                port:
                  number: 3000
    - host: api.grandcasino.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: grandcasino-api
                port:
                  number: 3000
    - host: socket.grandcasino.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: grandcasino-socket
                port:
                  number: 3000
    - host: lotto.grandcasino.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: grandcasino-lotto-web
                port:
                  number: 3000
    - host: admin-lotto.grandcasino.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: grandcasino-lotto-admin
                port:
                  number: 3000
    - host: api-lotto.grandcasino.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: grandcasino-lotto-api
                port:
                  number: 8080
  ingressClassName: nginx
