apiVersion: apps/v1
kind: Deployment
metadata:
  name: sample-app
  namespace: sample
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sample-app
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: sample-app
    spec:
      containers:
      - name: sample-app
        image: nginx:1.21
        imagePullPolicy: Always
        env:
          - name: ENV
            value: "production"
        ports:
        - name: http
          containerPort: 80


