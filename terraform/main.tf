terraform {
  # backend "pg" {
  #   conn_str             = ""
  #   schema_name          = ""
  #   skip_schema_creation = false
  # }
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    # http = {
    #   source  = "hashicorp/http"
    #   version = "3.4.3"
    # }
    local = {
      source  = "hashicorp/local"
      version = "2.5.1"
    }
    kubectl = {
      source  = "gavi<PERSON><PERSON><PERSON>/kubectl"
      version = ">= 1.7.0"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

data "digitalocean_kubernetes_cluster" "cluster" {
  name = "k8s-ops"
}

provider "kubernetes" {
  host    = data.digitalocean_kubernetes_cluster.cluster.endpoint
  token   = data.digitalocean_kubernetes_cluster.cluster.kube_config[0].token
  cluster_ca_certificate = base64decode(
    data.digitalocean_kubernetes_cluster.cluster.kube_config[0].cluster_ca_certificate
  )
}

# # Data source to get all namespaces in the cluster
# data "kubernetes_all_namespaces" "all" {}
#
# # Local value to get filtered namespaces for use in other resources
# locals {
#   filtered_namespaces = [
#     for ns in data.kubernetes_all_namespaces.all.namespaces : ns
#     if !contains([
#       "default",
#       "kube-node-lease",
#       "kube-public",
#       "kube-system"
#     ], ns)
#   ]
# }
#
# # Get deployments from each filtered namespace using kubectl provider
# data "kubectl_path_documents" "deployments" {
#   for_each = toset(local.filtered_namespaces)
#   pattern  = "${path.module}/deployments-${each.value}.yaml"
#
#   # This is a workaround - we'll use null_resource to generate the files
#   depends_on = [null_resource.get_deployments]
# }
#
# # Use null_resource to execute kubectl commands and save deployment info
# resource "null_resource" "get_deployments" {
#   for_each = toset(local.filtered_namespaces)
#
#   provisioner "local-exec" {
#     command = <<-EOT
#       kubectl get deployments -n ${each.value} -o yaml > ${path.module}/deployments-${each.value}.yaml || echo "apiVersion: v1\nkind: List\nitems: []" > ${path.module}/deployments-${each.value}.yaml
#     EOT
#   }
#
#   # Trigger re-execution when namespaces change
#   triggers = {
#     namespace = each.value
#   }
# }
#
# # Output deployments from all filtered namespaces
# output "namespace_deployments" {
#   description = "Deployments in each user namespace"
#   value = {
#     for ns in local.filtered_namespaces : ns => {
#       namespace = ns
#       deployment_files = try(data.kubectl_path_documents.deployments[ns].documents, [])
#     }
#   }
# }


resource "kubernetes_deployment" "deployment" {
  metadata {
    name      = "sample-app"
    namespace = "sample"
  }
  spec {
    replicas = 1
    selector {
      match_labels = {
        app = "sample-app"
      }
    }
    template {
      metadata {
        labels = {
          app = "sample-app"
        }
      }
      spec {
        container {
          image = "nginx:1.21"
          name  = "sample-app"
          image_pull_policy = "Always"
          port {
            name = "http"
            container_port = 80
          }
          env {
            name  = "ENV"
            value = "production"
          }
        }
      }
    }
  }
}


